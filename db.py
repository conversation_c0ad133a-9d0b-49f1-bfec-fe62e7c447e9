# db.py
import logging
from psycopg2.pool import ThreadedConnectionPool
from typing import Optional
from config import config

# 创建连接池（线程安全）
db_pool = ThreadedConnectionPool(
    minconn=config.DB_POOL_MIN_CONN,
    maxconn=config.DB_POOL_MAX_CONN,
    **config.DB_CONFIG
)

def init_db():
    """创建 PostgreSQL 表结构"""
    conn = db_pool.getconn()
    cursor = conn.cursor()
    
    try:
        # 商品基础信息表（新增 status 字段）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            sku_id BIGINT PRIMARY KEY,      
            name TEXT NOT NULL,              
            image_url TEXT,                  
            last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status INTEGER DEFAULT 1  -- 0: 下架, 1: 上架
        )
        ''')
        
        # 上架单主表（移除 sku_id 字段）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS listings (
            listing_id BIGINT PRIMARY KEY,  
            current_price NUMERIC(10,2),              
            original_price NUMERIC(10,2),                          
            seller_name TEXT,                
            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status INTEGER DEFAULT 1  -- 0: 下架, 1: 上架
        )
        ''')

        # 新增关联表：listing_products（新增）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS listing_products (
            listing_id BIGINT NOT NULL,
            sku_id BIGINT NOT NULL,
            PRIMARY KEY (listing_id, sku_id),
            FOREIGN KEY (listing_id) REFERENCES listings(listing_id) ON DELETE CASCADE,
            FOREIGN KEY (sku_id) REFERENCES products(sku_id) ON DELETE CASCADE
        )
        '''
        )
        
        # 全文搜索扩展
        cursor.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm;")
        
        # 商品名称全文索引表（修改：移除 UNIQUE 约束）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS product_names (
            id SERIAL PRIMARY KEY,
            name TEXT NOT NULL,  
            search_vector TSVECTOR
        )
        ''')
        
        # 创建 GIN 索引
        cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_product_names_search 
        ON product_names USING GIN(search_vector)
        ''')
        
        # 触发器函数（修改：允许多个相同名称插入）
        cursor.execute('''
        CREATE OR REPLACE FUNCTION update_product_names()
        RETURNS TRIGGER AS $$
        BEGIN
            IF TG_OP = 'INSERT' THEN
                INSERT INTO product_names (name, search_vector)
                VALUES (NEW.name, to_tsvector(NEW.name));  
            ELSIF TG_OP = 'UPDATE' AND OLD.name != NEW.name THEN
                DELETE FROM product_names WHERE name = OLD.name;
                INSERT INTO product_names (name, search_vector)
                VALUES (NEW.name, to_tsvector(NEW.name));
            END IF;
            RETURN NULL;
        END;
        $$ LANGUAGE plpgsql;
        ''')
        
        # 创建触发器（增加存在性检查）
        cursor.execute('''
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 
                FROM pg_trigger 
                WHERE tgname = 'product_name_changes'
            ) THEN
                CREATE TRIGGER product_name_changes
                AFTER INSERT OR UPDATE OF name ON products
                FOR EACH ROW
                EXECUTE FUNCTION update_product_names();
            END IF;
        END $$;
        ''')

        # 新增表：记录 last_next_id（类型为 TEXT）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS last_next_id (
            id SERIAL PRIMARY KEY,
            next_id TEXT
        )
        ''')
        
        conn.commit()
    except Exception as e:
        logging.error(f"数据库初始化错误: {str(e)}")
        conn.rollback()
    finally:
        db_pool.putconn(conn)

def get_last_next_id(cursor) -> Optional[str]:
    """从数据库获取最后一次的 nextId，确保返回字符串"""
    try:
        cursor.execute("SELECT next_id FROM last_next_id ORDER BY id DESC LIMIT 1")
        result = cursor.fetchone()
        return str(result[0]) if result and result[0] is not None else None  # 返回字符串类型
    except Exception as e:
        logging.error(f"获取 last_next_id 失败: {str(e)}")
        return None

def save_last_next_id(next_id, conn, cursor):
    """将最新的 nextId 存入数据库，仅允许字符串类型"""
    try:
        cursor.execute("DELETE FROM last_next_id")
        if next_id is not None:
            # 强制转换为字符串类型
            cursor.execute("INSERT INTO last_next_id (next_id) VALUES (%s)", (str(next_id),))  # 存储前强制转字符串
        conn.commit()
    except Exception as e:
        logging.error(f"保存 next_id 失败: {str(e)}")
        conn.rollback()

# 初始化数据库
init_db()