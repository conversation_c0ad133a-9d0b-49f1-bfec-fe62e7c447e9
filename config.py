# config.py
import os
from typing import Dict, Any

class Config:
    """应用配置类"""
    
    # Flask配置
    FLASK_HOST = os.getenv('FLASK_HOST', '0.0.0.0')
    FLASK_PORT = int(os.getenv('FLASK_PORT', '5000'))
    FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 数据库配置
    DB_CONFIG = {
        "dbname": os.getenv("DB_NAME", "bilibili_c2c_prod"),
        "user": os.getenv("DB_USER", "postgres"),
        "password": os.getenv("DB_PASSWORD", "986428"),
        "host": os.getenv("DB_HOST", "127.0.0.1"),
        "port": os.getenv("DB_PORT", "6666")
    }
    
    # 连接池配置
    DB_POOL_MIN_CONN = int(os.getenv('DB_POOL_MIN_CONN', '1'))
    DB_POOL_MAX_CONN = int(os.getenv('DB_POOL_MAX_CONN', '5'))
    
    # 爬虫配置
    SPIDER_DELAY = int(os.getenv('SPIDER_DELAY', '5'))  # 请求间隔秒数
    SPIDER_TIMEOUT = int(os.getenv('SPIDER_TIMEOUT', '10'))  # 请求超时秒数
    SPIDER_RETRY_TIMES = int(os.getenv('SPIDER_RETRY_TIMES', '3'))  # 重试次数
    
    # API配置
    BILIBILI_API_URL = os.getenv('BILIBILI_API_URL', 'https://mall.bilibili.com/mall-magic-c/internet/c2c/v2/list')
    CATEGORY_FILTER = os.getenv('CATEGORY_FILTER', '2312')
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 默认请求头配置
    DEFAULT_HEADERS = {
        'authority': 'mall.bilibili.com',
        'method': 'POST',
        'path': '/mall-magic-c/internet/c2c/v2/list',
        'scheme': 'https',
        'accept': 'application/json, text/plain, */*',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://mall.bilibili.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://mall.bilibili.com/neul-next/index.html?page=magic-market_index',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'sec-gpc': '1',
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'
    }
    
    # 默认Cookie（建议通过环境变量设置）
    DEFAULT_COOKIE = os.getenv('DEFAULT_COOKIE', 
        "buvid4=9423B4FA-23D6-7C9C-6D87-A65BFB6C92C073969-023101115-A47OU7FO4Tt7UJBkBBidHQ%3D%3D; "
        "home_feed_column=5; enable_web_push=DISABLE; header_theme_version=CLOSE; PVID=1; "
        "DedeUserID=21995788; DedeUserID__ckMd5=74533ce01e81c457; LIVE_BUVID=AUTO2417163479959397; "
        "fingerprint=d4fd28d46f0cdf7f6c4c4bd4cffc4232; browser_resolution=1494-765; "
        "buvid3=BCD23078-F90D-C047-149F-3130A5299F6B27399infoc; b_nut=1728617127; "
        "_uuid=C9D3E15B-812C-7E68-EAB3-824B5A97E53428151infoc; buvid_fp=717cf89234532f9f32c7a8860c589d96; "
        "rpdid=|(umRR)Yu)|k0J\\'u~JkY|Rl)m; enable_feed_channel=ENABLE; CURRENT_FNVAL=4048; "
        "SESSDATA=ae848d53%2C1762744517%2Cc9f15%2A52CjB4iZUl6gWWq_YetxVdmpa7IobyabdVgg6fuXoD4jxV7mxxlFTpkma4X8zpETZ40RISVkY4NVdVN2RzTWo4TEZTdWRfdnRuYXl4c205ZDFxMVp4NDFGWEVUUVB6TVBUcl9vSmREdEh4R3BKTWZiZHVNT1ZpdHdQcHBZX1lsZEF0aERuZk9UR0tnIIEC; "
        "bili_jct=7954f256b7"
    )
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置是否有效"""
        try:
            # 验证数据库配置
            required_db_keys = ['dbname', 'user', 'password', 'host', 'port']
            for key in required_db_keys:
                if not cls.DB_CONFIG.get(key):
                    raise ValueError(f"数据库配置缺少必要参数: {key}")
            
            # 验证端口号
            if not (1 <= cls.FLASK_PORT <= 65535):
                raise ValueError(f"Flask端口号无效: {cls.FLASK_PORT}")
                
            if not (1 <= int(cls.DB_CONFIG['port']) <= 65535):
                raise ValueError(f"数据库端口号无效: {cls.DB_CONFIG['port']}")
            
            # 验证连接池配置
            if cls.DB_POOL_MIN_CONN <= 0 or cls.DB_POOL_MAX_CONN <= 0:
                raise ValueError("数据库连接池配置无效")
                
            if cls.DB_POOL_MIN_CONN > cls.DB_POOL_MAX_CONN:
                raise ValueError("最小连接数不能大于最大连接数")
            
            return True
            
        except Exception as e:
            print(f"配置验证失败: {e}")
            return False

# 创建全局配置实例
config = Config()
