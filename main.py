# main.py
import logging

from routes import app
from config import config

# 尝试加载环境变量（可选）
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # python-dotenv 未安装，跳过

# 设置日志
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    try:
        # 验证配置
        if not config.validate_config():
            logger.error("配置验证失败，程序退出")
            return

        logger.info(f"启动Flask应用，监听 {config.FLASK_HOST}:{config.FLASK_PORT}")
        app.run(
            host=config.FLASK_HOST,
            port=config.FLASK_PORT,
            debug=config.FLASK_DEBUG
        )

    except Exception as e:
        logger.error(f"应用启动失败: {e}", exc_info=True)

if __name__ == "__main__":
    main()