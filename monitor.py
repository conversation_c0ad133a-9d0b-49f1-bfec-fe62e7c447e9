# monitor.py
"""性能监控模块"""

import time
import psutil
import threading
from typing import Dict, Any
from dataclasses import dataclass, asdict
from utils import get_task_status

@dataclass
class SystemMetrics:
    """系统指标"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_total_mb: float
    disk_percent: float
    network_sent_mb: float
    network_recv_mb: float
    timestamp: float

@dataclass
class ApplicationMetrics:
    """应用指标"""
    task_status: str
    current_page: int
    running_duration: float
    error_count: int
    success_count: int
    timestamp: float

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.is_monitoring = False
        self.monitor_thread = None
        self.metrics_history = []
        self.max_history_size = 100
        self.error_count = 0
        self.success_count = 0
        self.last_network_stats = None
        
    def start_monitoring(self, interval: int = 30):
        """开始监控"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop, 
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
            
    def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 收集系统指标
                system_metrics = self._collect_system_metrics()
                
                # 收集应用指标
                app_metrics = self._collect_app_metrics()
                
                # 存储指标
                self._store_metrics(system_metrics, app_metrics)
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"监控异常: {e}")
                time.sleep(interval)
                
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / 1024 / 1024
        memory_total_mb = memory.total / 1024 / 1024
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        
        # 网络使用情况
        network = psutil.net_io_counters()
        if self.last_network_stats:
            network_sent_mb = (network.bytes_sent - self.last_network_stats.bytes_sent) / 1024 / 1024
            network_recv_mb = (network.bytes_recv - self.last_network_stats.bytes_recv) / 1024 / 1024
        else:
            network_sent_mb = 0
            network_recv_mb = 0
        self.last_network_stats = network
        
        return SystemMetrics(
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            memory_total_mb=memory_total_mb,
            disk_percent=disk_percent,
            network_sent_mb=network_sent_mb,
            network_recv_mb=network_recv_mb,
            timestamp=time.time()
        )
        
    def _collect_app_metrics(self) -> ApplicationMetrics:
        """收集应用指标"""
        task_status = get_task_status()
        
        # 计算运行时长
        running_duration = 0
        if task_status.get("start_time") and task_status["status"] == "running":
            running_duration = time.time() - task_status["start_time"]
        elif task_status.get("start_time") and task_status.get("end_time"):
            running_duration = task_status["end_time"] - task_status["start_time"]
            
        return ApplicationMetrics(
            task_status=task_status["status"],
            current_page=task_status["current_page"],
            running_duration=running_duration,
            error_count=self.error_count,
            success_count=self.success_count,
            timestamp=time.time()
        )
        
    def _store_metrics(self, system_metrics: SystemMetrics, app_metrics: ApplicationMetrics):
        """存储指标"""
        metrics = {
            "system": asdict(system_metrics),
            "application": asdict(app_metrics)
        }
        
        self.metrics_history.append(metrics)
        
        # 限制历史记录大小
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history.pop(0)
            
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标"""
        if not self.metrics_history:
            return {}
        return self.metrics_history[-1]
        
    def get_metrics_history(self, limit: int = 10) -> list:
        """获取指标历史"""
        return self.metrics_history[-limit:]
        
    def increment_error_count(self):
        """增加错误计数"""
        self.error_count += 1
        
    def increment_success_count(self):
        """增加成功计数"""
        self.success_count += 1

# 全局监控器实例
performance_monitor = PerformanceMonitor()
