version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - DB_HOST=postgres
      - DB_NAME=bilibili_c2c_prod
      - DB_USER=postgres
      - DB_PASSWORD=postgres123
      - DB_PORT=5432
      - FLASK_DEBUG=False
    depends_on:
      - postgres
    volumes:
      - .:/app
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=bilibili_c2c_prod
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

volumes:
  postgres_data:
