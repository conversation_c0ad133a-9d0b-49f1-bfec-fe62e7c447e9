# exceptions.py
"""自定义异常类"""

class BiliMarketException(Exception):
    """基础异常类"""
    pass

class ConfigurationError(BiliMarketException):
    """配置错误"""
    pass

class DatabaseError(BiliMarketException):
    """数据库错误"""
    pass

class SpiderError(BiliMarketException):
    """爬虫错误"""
    pass

class NetworkError(SpiderError):
    """网络错误"""
    pass

class DataParseError(SpiderError):
    """数据解析错误"""
    pass
