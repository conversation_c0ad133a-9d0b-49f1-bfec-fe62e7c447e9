-- init.sql
-- 数据库初始化脚本

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- 商品基础信息表
CREATE TABLE IF NOT EXISTS products (
    sku_id BIGINT PRIMARY KEY,      
    name TEXT NOT NULL,              
    image_url TEXT,                  
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status INTEGER DEFAULT 1  -- 0: 下架, 1: 上架
);

-- 上架单主表
CREATE TABLE IF NOT EXISTS listings (
    listing_id BIGINT PRIMARY KEY,  
    current_price NUMERIC(10,2),              
    original_price NUMERIC(10,2),                          
    seller_name TEXT,                
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status INTEGER DEFAULT 1  -- 0: 下架, 1: 上架
);

-- 关联表：listing_products
CREATE TABLE IF NOT EXISTS listing_products (
    listing_id BIGINT NOT NULL,
    sku_id BIGINT NOT NULL,
    PRIMARY KEY (listing_id, sku_id),
    FOREIGN KEY (listing_id) REFERENCES listings(listing_id) ON DELETE CASCADE,
    FOREIGN KEY (sku_id) REFERENCES products(sku_id) ON DELETE CASCADE
);

-- 商品名称全文索引表
CREATE TABLE IF NOT EXISTS product_names (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,  
    search_vector TSVECTOR
);

-- 创建 GIN 索引
CREATE INDEX IF NOT EXISTS idx_product_names_search 
ON product_names USING GIN(search_vector);

-- 记录 last_next_id
CREATE TABLE IF NOT EXISTS last_next_id (
    id SERIAL PRIMARY KEY,
    next_id TEXT
);

-- 触发器函数
CREATE OR REPLACE FUNCTION update_product_names()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO product_names (name, search_vector)
        VALUES (NEW.name, to_tsvector(NEW.name));  
    ELSIF TG_OP = 'UPDATE' AND OLD.name != NEW.name THEN
        DELETE FROM product_names WHERE name = OLD.name;
        INSERT INTO product_names (name, search_vector)
        VALUES (NEW.name, to_tsvector(NEW.name));
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_trigger 
        WHERE tgname = 'product_name_changes'
    ) THEN
        CREATE TRIGGER product_name_changes
        AFTER INSERT OR UPDATE OF name ON products
        FOR EACH ROW
        EXECUTE FUNCTION update_product_names();
    END IF;
END $$;
