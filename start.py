#!/usr/bin/env python3
# start.py
"""项目启动脚本"""

import sys
import os
import subprocess
import logging

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask',
        'psycopg2-binary',
        'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        sys.exit(1)

def setup_environment():
    """设置环境"""
    # 检查.env文件
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            print("提示: 请复制 .env.example 为 .env 并配置相关参数")
        else:
            print("提示: 请创建 .env 文件并配置环境变量")

def main():
    """主函数"""
    print("=== B站商城爬虫项目启动 ===")
    
    # 检查环境
    print("1. 检查Python版本...")
    check_python_version()
    print("   ✓ Python版本检查通过")
    
    print("2. 检查依赖包...")
    check_dependencies()
    print("   ✓ 依赖包检查通过")
    
    print("3. 设置环境...")
    setup_environment()
    print("   ✓ 环境设置完成")
    
    print("4. 启动应用...")
    try:
        # 导入并启动应用
        from main import main as app_main
        app_main()
    except KeyboardInterrupt:
        print("\n应用已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
