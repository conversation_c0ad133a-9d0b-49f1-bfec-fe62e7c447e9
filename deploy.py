#!/usr/bin/env python3
# deploy.py
"""部署脚本"""

import sys
import subprocess
import argparse
from pathlib import Path

class Deployer:
    """部署器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        
    def check_docker(self):
        """检查Docker是否可用"""
        try:
            subprocess.run(['docker', '--version'], 
                         check=True, capture_output=True)
            subprocess.run(['docker-compose', '--version'], 
                         check=True, capture_output=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
            
    def check_env_file(self):
        """检查环境变量文件"""
        env_file = self.project_root / '.env'
        if not env_file.exists():
            print("警告: .env 文件不存在")
            example_file = self.project_root / '.env.example'
            if example_file.exists():
                print("请复制 .env.example 为 .env 并配置相关参数")
            return False
        return True
        
    def deploy_docker(self):
        """Docker部署"""
        print("=== Docker部署 ===")
        
        if not self.check_docker():
            print("错误: Docker或docker-compose未安装")
            return False
            
        try:
            # 构建并启动服务
            print("1. 构建Docker镜像...")
            subprocess.run(['docker-compose', 'build'], 
                         cwd=self.project_root, check=True)
            
            print("2. 启动服务...")
            subprocess.run(['docker-compose', 'up', '-d'], 
                         cwd=self.project_root, check=True)
            
            print("3. 检查服务状态...")
            subprocess.run(['docker-compose', 'ps'], 
                         cwd=self.project_root, check=True)
            
            print("\n✓ Docker部署完成!")
            print("应用地址: http://localhost:5000")
            print("查看日志: docker-compose logs -f")
            print("停止服务: docker-compose down")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"Docker部署失败: {e}")
            return False
            
    def deploy_local(self):
        """本地部署"""
        print("=== 本地部署 ===")
        
        try:
            # 检查Python版本
            if sys.version_info < (3, 8):
                print("错误: 需要Python 3.8或更高版本")
                return False
                
            # 安装依赖
            print("1. 安装依赖...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                         cwd=self.project_root, check=True)
            
            # 检查环境文件
            print("2. 检查环境配置...")
            self.check_env_file()
            
            print("3. 启动应用...")
            print("请运行: python start.py")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"本地部署失败: {e}")
            return False
            
    def deploy_production(self):
        """生产环境部署"""
        print("=== 生产环境部署 ===")
        
        # 检查环境文件
        if not self.check_env_file():
            print("错误: 生产环境必须配置 .env 文件")
            return False
            
        # 使用Docker部署
        return self.deploy_docker()
        
    def show_status(self):
        """显示服务状态"""
        if self.check_docker():
            try:
                subprocess.run(['docker-compose', 'ps'], 
                             cwd=self.project_root, check=True)
            except subprocess.CalledProcessError:
                print("Docker服务未运行")
        else:
            print("Docker不可用，无法查看状态")
            
    def stop_services(self):
        """停止服务"""
        if self.check_docker():
            try:
                subprocess.run(['docker-compose', 'down'], 
                             cwd=self.project_root, check=True)
                print("服务已停止")
            except subprocess.CalledProcessError as e:
                print(f"停止服务失败: {e}")
        else:
            print("Docker不可用")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='B站商城爬虫部署脚本')
    parser.add_argument('action', choices=['docker', 'local', 'production', 'status', 'stop'],
                       help='部署动作')
    
    args = parser.parse_args()
    deployer = Deployer()
    
    if args.action == 'docker':
        deployer.deploy_docker()
    elif args.action == 'local':
        deployer.deploy_local()
    elif args.action == 'production':
        deployer.deploy_production()
    elif args.action == 'status':
        deployer.show_status()
    elif args.action == 'stop':
        deployer.stop_services()

if __name__ == "__main__":
    main()
