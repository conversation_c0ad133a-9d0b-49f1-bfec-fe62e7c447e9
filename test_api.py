# test_api.py
"""API测试脚本"""
import requests
import json
from typing import Dict, Any

class BiliMarketAPITester:
    """B站商城API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        
    def test_status(self) -> Dict[str, Any]:
        """测试状态接口"""
        try:
            response = requests.get(f"{self.base_url}/api/status")
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "data": response.json() if response.status_code == 200 else response.text
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_health(self) -> Dict[str, Any]:
        """测试健康检查接口"""
        try:
            response = requests.get(f"{self.base_url}/api/health")
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "data": response.json() if response.status_code == 200 else response.text
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_start(self, cookie: str = "", mode: str = "restart") -> Dict[str, Any]:
        """测试启动接口"""
        try:
            payload = {"cookie": cookie, "mode": mode}
            response = requests.post(
                f"{self.base_url}/api/start",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "data": response.json() if response.status_code in [200, 400] else response.text
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_stop(self) -> Dict[str, Any]:
        """测试停止接口"""
        try:
            response = requests.post(f"{self.base_url}/api/stop")
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "data": response.json() if response.status_code in [200, 400] else response.text
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def run_all_tests(self) -> None:
        """运行所有测试"""
        print("=== B站商城API测试 ===\n")
        
        # 测试健康检查
        print("1. 测试健康检查接口...")
        result = self.test_health()
        print(f"   结果: {'✓' if result['success'] else '✗'}")
        print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}\n")
        
        # 测试状态接口
        print("2. 测试状态接口...")
        result = self.test_status()
        print(f"   结果: {'✓' if result['success'] else '✗'}")
        print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}\n")
        
        # 测试启动接口（无效模式）
        print("3. 测试启动接口（无效模式）...")
        result = self.test_start(mode="invalid")
        print(f"   结果: {'✓' if not result['success'] else '✗'}")
        print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}\n")

if __name__ == "__main__":
    tester = BiliMarketAPITester()
    tester.run_all_tests()
