#!/usr/bin/env python3
# cleanup_unused.py
"""清理未使用的导入和变量的脚本"""

import ast
import os
from pathlib import Path
from typing import Set, List, Dict

class UnusedImportFinder(ast.NodeVisitor):
    """查找未使用的导入"""
    
    def __init__(self):
        self.imports: Dict[str, int] = {}
        self.used_names: Set[str] = set()
        
    def visit_Import(self, node):
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name
            self.imports[name] = node.lineno
        self.generic_visit(node)
        
    def visit_ImportFrom(self, node):
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name
            self.imports[name] = node.lineno
        self.generic_visit(node)
        
    def visit_Name(self, node):
        if isinstance(node.ctx, ast.Load):
            self.used_names.add(node.id)
        self.generic_visit(node)
        
    def visit_Attribute(self, node):
        if isinstance(node.value, ast.Name):
            self.used_names.add(node.value.id)
        self.generic_visit(node)

def find_unused_imports(file_path: str) -> List[str]:
    """查找文件中未使用的导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        tree = ast.parse(content)
        finder = UnusedImportFinder()
        finder.visit(tree)
        
        unused = []
        for import_name, line_no in finder.imports.items():
            if import_name not in finder.used_names:
                unused.append(f"Line {line_no}: {import_name}")
                
        return unused
        
    except Exception as e:
        return [f"Error parsing {file_path}: {e}"]

def check_project_files():
    """检查项目中的所有Python文件"""
    project_root = Path(__file__).parent
    python_files = list(project_root.glob("*.py"))
    
    print("=== 未使用的导入检查 ===\n")
    
    for file_path in python_files:
        if file_path.name == "cleanup_unused.py":
            continue
            
        print(f"检查文件: {file_path.name}")
        unused = find_unused_imports(str(file_path))
        
        if unused:
            print("  未使用的导入:")
            for item in unused:
                print(f"    {item}")
        else:
            print("  ✓ 没有发现未使用的导入")
        print()

def main():
    """主函数"""
    check_project_files()
    
    print("注意:")
    print("1. 某些导入可能在字符串中使用，工具无法检测到")
    print("2. 动态导入和反射使用的模块可能被误报")
    print("3. 请手动验证结果后再删除导入")

if __name__ == "__main__":
    main()
