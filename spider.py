# spider.py
import requests
import time
import logging
from typing import Dict, Any, Optional, List, Tuple

from db import db_pool, get_last_next_id, save_last_next_id
from utils import update_task_status, get_task_status
from config import config
import share

logger = logging.getLogger(__name__)
def parse_response(data: Dict[str, Any], conn, cursor) -> Optional[str]:
    """解析响应并写入PostgreSQL数据库（优化批量插入）"""
    try:
        items = data.get('data', {}).get('data', [])
        if not items:
            return None

        product_data: List[Tuple] = []
        listing_data: List[Tuple] = []
        listing_product_data: List[Tuple] = []  # 存储 listing_id 和 sku_id 的关联关系
        
        for item in items:
            # 处理多个 detailDtoList 中的商品
            for detail in item['detailDtoList']:
                sku_id = int(detail['skuId'])          
                listing_id = int(item['c2cItemsId'])   
                
                # 商品数据（保持唯一性）
                product_data.append((
                    sku_id, 
                    item['c2cItemsName'], 
                    "https:" + detail['img']
                ))
                
                # 上架单关联数据（新增）
                listing_product_data.append((
                    listing_id,
                    sku_id
                ))

            # 上架单信息（新增）
            current_price = float(item.get('showPrice', 0))
            original_price = float(item.get('showMarketPrice', 0))
            seller_name = item.get('sellerName', '')
            listing_data.append((
                listing_id,
                current_price,
                original_price,
                seller_name
            ))

        # 插入商品信息
        if product_data:
            cursor.executemany('''
                INSERT INTO products (sku_id, name, image_url)
                VALUES (%s, %s, %s)
                ON CONFLICT (sku_id) DO UPDATE SET
                    name = EXCLUDED.name,
                    image_url = EXCLUDED.image_url,
                    last_modified = CURRENT_TIMESTAMP
                WHERE products.name != EXCLUDED.name 
                   OR products.image_url != EXCLUDED.image_url
            ''', product_data)
            
        # 批量插入上架单信息
        if listing_data:
            cursor.executemany('''
                INSERT INTO listings (
                    listing_id, 
                    current_price, 
                    original_price, 
                    seller_name
                )
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (listing_id) DO UPDATE SET
                    current_price = EXCLUDED.current_price,
                    original_price = EXCLUDED.original_price,
                    seller_name = EXCLUDED.seller_name,
                    update_time = CURRENT_TIMESTAMP
            ''', listing_data)
            
        # 批量插入上架单与商品的关联关系（新增）
        if listing_product_data:
            cursor.executemany('''
                INSERT INTO listing_products (listing_id, sku_id)
                VALUES (%s, %s)
                ON CONFLICT (listing_id, sku_id) DO NOTHING
            ''', listing_product_data)
            
        conn.commit()
        
        # 返回下一页ID
        next_id = data.get('data', {}).get('nextId')  # 直接返回原始字符串
        return next_id
    except ValueError as ve:
        logging.error(f"ID转换失败: {str(ve)}", exc_info=True)
        conn.rollback()
    except Exception as e:
        logging.error(f"插入数据失败: {str(e)}", exc_info=True)
        conn.rollback()

def fetch_all_pages(headers: Dict[str, str], initial_next_id: Optional[str] = None) -> None:
    """持续抓取所有页面直到完成或被中断"""
    conn = db_pool.getconn()
    cursor = conn.cursor()
    next_id = initial_next_id
    page = 1
    retry_count = 0

    try:
        while True:
            # 检查停止标志
            if share.stop_flag:
                logger.info("检测到停止信号，准备终止抓取...")
                update_task_status(status="stopped")
                break

            # 构建请求参数
            payload = {"categoryFilter": config.CATEGORY_FILTER}
            if next_id is not None:
                payload["nextId"] = next_id

            logger.info(f"正在抓取第{page}页数据...")

            # 发送请求
            try:
                response = requests.post(
                    config.BILIBILI_API_URL,
                    json=payload,
                    headers=headers,
                    timeout=config.SPIDER_TIMEOUT
                )
                
                if response.status_code != 200:
                    error_msg = f"请求失败，状态码：{response.status_code}"
                    logging.error(error_msg)
                    update_task_status(status="error", error=error_msg)
                    break
                    
                data = response.json()
                
            except requests.exceptions.RequestException as re:
                error_msg = f"网络请求异常: {str(re)}"
                logging.error(error_msg, exc_info=True)
                update_task_status(status="error", error=error_msg)
                break
            except ValueError as ve:
                error_msg = f"JSON解析失败: {str(ve)}"
                logging.error(error_msg, exc_info=True)
                update_task_status(status="error", error=error_msg)
                break

            # 解析响应并保存数据
            next_id = parse_response(data, conn, cursor)
            save_last_next_id(next_id, conn, cursor)
            update_task_status(page=page, error="")
            
            # 检查是否到达最后一页
            if not next_id:
                logging.info("已到达最后一页")
                save_last_next_id(None, conn, cursor)  # 清空 last_next_id 表
                update_task_status(status="completed")
                break
                
            page += 1
            retry_count = 0  # 重置重试计数
            time.sleep(config.SPIDER_DELAY)

    except Exception as e:
        error_msg = f"抓取过程中发生异常: {str(e)}"
        update_task_status(status="error", error=error_msg)
        logger.error(error_msg, exc_info=True)

    finally:
        db_pool.putconn(conn)
        logger.info("数据抓取完成，已保存至PostgreSQL数据库")
        # 重置停止标志
        share.stop_flag = False

def run_spider(headers: Dict[str, str], mode: str = "restart") -> None:
    """爬虫执行线程"""
    try:
        initial_next_id = None
        if mode == "continue":
            conn = db_pool.getconn()
            cursor = conn.cursor()
            initial_next_id = get_last_next_id(cursor)
            db_pool.putconn(conn)

        # 开始抓取任务
        fetch_all_pages(headers, initial_next_id)

    except Exception as e:
        error_msg = f"爬虫线程异常终止: {str(e)}"
        logger.error(error_msg, exc_info=True)
        current_status = get_task_status()
        if current_status["status"] != "error":
            update_task_status(status="error", error=error_msg)
    finally:
        # 确保即使出现异常也能更新状态
        current_status = get_task_status()
        if current_status["status"] not in ("error", "completed", "stopped"):
            update_task_status(status="unknown")