# Flask配置
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=False

# 数据库配置
DB_NAME=bilibili_c2c_prod
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_HOST=127.0.0.1
DB_PORT=6666

# 连接池配置
DB_POOL_MIN_CONN=1
DB_POOL_MAX_CONN=5

# 爬虫配置
SPIDER_DELAY=5
SPIDER_TIMEOUT=10
SPIDER_RETRY_TIMES=3

# API配置
BILIBILI_API_URL=https://mall.bilibili.com/mall-magic-c/internet/c2c/v2/list
CATEGORY_FILTER=2312

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Cookie配置（请替换为您的实际Cookie）
DEFAULT_COOKIE=your_cookie_here
