# models.py
"""数据模型定义"""
from dataclasses import dataclass
from typing import Optional
from datetime import datetime

@dataclass
class Product:
    """商品模型"""
    sku_id: int
    name: str
    image_url: str
    last_modified: Optional[datetime] = None
    status: int = 1  # 0: 下架, 1: 上架

@dataclass
class Listing:
    """上架单模型"""
    listing_id: int
    current_price: float
    original_price: float
    seller_name: str
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    status: int = 1  # 0: 下架, 1: 上架

@dataclass
class ListingProduct:
    """上架单商品关联模型"""
    listing_id: int
    sku_id: int

@dataclass
class TaskStatus:
    """任务状态模型"""
    status: str  # running, stopped, error, completed
    current_page: int = 0
    error: str = ""
    next_id: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None

@dataclass
class SpiderRequest:
    """爬虫请求模型"""
    cookie: str = ""
    mode: str = "restart"  # restart, continue
    
    def __post_init__(self):
        if self.mode not in ["restart", "continue"]:
            raise ValueError(f"无效的模式: {self.mode}")

@dataclass
class ApiResponse:
    """API响应数据模型"""
    data: dict
    next_id: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ApiResponse':
        """从字典创建响应对象"""
        response_data = data.get('data', {})
        next_id = response_data.get('nextId')
        return cls(data=data, next_id=next_id)
