# utils.py
import threading
import logging

# 新增全局状态变量（合并状态字段）

task_status = {
    "status": "stopped",  # 可能的值:  running, stopped, error
    "current_page": 0,
    "error": "",
    "next_id": None
}

# 新增线程锁
status_lock = threading.Lock()

def update_task_status(status=None, page=None, error=None):
    """线程安全的任务状态更新"""
    with status_lock:  # 使用锁保护共享资源
        if status is not None:
            task_status["status"] = status
        if page is not None:
            task_status["current_page"] = page
        if error is not None:
            task_status["error"] = error

# 初始化日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)