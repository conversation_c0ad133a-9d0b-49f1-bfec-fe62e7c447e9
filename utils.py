# utils.py
import threading
import logging
from typing import Optional, Dict, Any
from config import config

# 全局状态变量
task_status: Dict[str, Any] = {
    "status": "stopped",  # 可能的值: running, stopped, error, completed
    "current_page": 0,
    "error": "",
    "next_id": None,
    "start_time": None,
    "end_time": None
}

# 线程锁
status_lock = threading.Lock()

def update_task_status(status: Optional[str] = None,
                      page: Optional[int] = None,
                      error: Optional[str] = None,
                      next_id: Optional[str] = None) -> None:
    """线程安全的任务状态更新"""
    with status_lock:
        if status is not None:
            task_status["status"] = status
            if status == "running":
                import time
                task_status["start_time"] = time.time()
                task_status["end_time"] = None
            elif status in ("stopped", "error", "completed"):
                import time
                task_status["end_time"] = time.time()

        if page is not None:
            task_status["current_page"] = page
        if error is not None:
            task_status["error"] = error
        if next_id is not None:
            task_status["next_id"] = next_id

def get_task_status() -> Dict[str, Any]:
    """获取当前任务状态（线程安全）"""
    with status_lock:
        return task_status.copy()

def setup_logging() -> None:
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL.upper()),
        format=config.LOG_FORMAT,
        force=True  # 强制重新配置
    )

# 初始化日志配置
setup_logging()