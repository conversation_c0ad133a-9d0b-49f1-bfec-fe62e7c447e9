# B站商城爬虫项目

一个用于抓取B站商城商品数据的Python爬虫项目，提供RESTful API接口进行任务管理。

## 功能特性

- 🚀 **高性能爬虫**: 支持多线程数据抓取
- 🗄️ **数据库存储**: 使用PostgreSQL存储商品和上架单数据
- 🔄 **任务管理**: 支持启动、停止、继续爬虫任务
- 📊 **状态监控**: 实时查看爬虫运行状态
- 🔧 **配置管理**: 支持环境变量配置
- 🛡️ **错误处理**: 完善的异常处理和重试机制

## 项目结构

```
bili-market/
├── main.py              # 应用入口
├── routes.py            # API路由定义
├── spider.py            # 爬虫核心逻辑
├── db.py               # 数据库操作
├── config.py           # 配置管理
├── utils.py            # 工具函数
├── share.py            # 共享变量
├── models.py           # 数据模型
├── exceptions.py       # 自定义异常
├── test_api.py         # API测试脚本
├── requirements.txt    # 依赖包列表
├── .env.example       # 环境变量示例
└── README.md          # 项目文档
```

## 安装部署

### 1. 环境要求

- Python 3.8+
- PostgreSQL 12+

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接等信息：
```env
# 数据库配置
DB_NAME=bilibili_c2c_prod
DB_USER=postgres
DB_PASSWORD=your_password
DB_HOST=127.0.0.1
DB_PORT=6666

# Flask配置
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=False

# 爬虫配置
SPIDER_DELAY=5
SPIDER_TIMEOUT=10
DEFAULT_COOKIE=your_cookie_here
```

### 4. 启动应用

```bash
python main.py
```

## API接口

### 启动爬虫任务

```http
POST /api/start
Content-Type: application/json

{
    "cookie": "your_cookie_here",
    "mode": "restart"  // restart 或 continue
}
```

### 停止爬虫任务

```http
POST /api/stop
```

### 查看任务状态

```http
GET /api/status
```

### 重置任务进度

```http
POST /api/reset
```

## 数据库表结构

### products (商品表)
- `sku_id`: 商品SKU ID (主键)
- `name`: 商品名称
- `image_url`: 商品图片URL
- `last_modified`: 最后修改时间
- `status`: 状态 (0:下架, 1:上架)

### listings (上架单表)
- `listing_id`: 上架单ID (主键)
- `current_price`: 当前价格
- `original_price`: 原价
- `seller_name`: 卖家名称
- `create_time`: 创建时间
- `update_time`: 更新时间
- `status`: 状态 (0:下架, 1:上架)

### listing_products (关联表)
- `listing_id`: 上架单ID
- `sku_id`: 商品SKU ID

## 配置说明

### 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `DB_NAME` | bilibili_c2c_prod | 数据库名称 |
| `DB_USER` | postgres | 数据库用户名 |
| `DB_PASSWORD` | - | 数据库密码 |
| `DB_HOST` | 127.0.0.1 | 数据库主机 |
| `DB_PORT` | 6666 | 数据库端口 |
| `FLASK_HOST` | 0.0.0.0 | Flask监听地址 |
| `FLASK_PORT` | 5000 | Flask监听端口 |
| `SPIDER_DELAY` | 5 | 请求间隔(秒) |
| `SPIDER_TIMEOUT` | 10 | 请求超时(秒) |
| `DEFAULT_COOKIE` | - | 默认Cookie |

## 测试

运行API测试：
```bash
python test_api.py
```

## 注意事项

1. **Cookie配置**: 请确保配置有效的B站Cookie，否则可能无法正常抓取数据
2. **请求频率**: 建议设置合理的请求间隔，避免对目标网站造成过大压力
3. **数据库权限**: 确保数据库用户有创建表和索引的权限
4. **网络环境**: 确保服务器能正常访问B站商城API

## 许可证

MIT License
