# routes.py
from flask import Flask, request, jsonify
import threading
import time
import share
from spider import run_spider
import utils  # 替换 from utils import task_status, stop_flag, update_task_status
from db import db_pool, init_db

app = Flask(__name__)

@app.route('/api/start', methods=['POST'])
def start_task():
    try:
        if request.is_json:
            cookie_json = request.json.get('cookie', '')
            mode = request.json.get('mode', 'restart')  
        else:
            cookie_json = ''
            mode = 'restart'  # 默认模式改为重启
        
        default_cookie = "buvid4=9423B4FA-23D6-7C9C-6D87-A65BFB6C92C073969-023101115-A47OU7FO4Tt7UJBkBBidHQ%3D%3D; home_feed_column=5; enable_web_push=DISABLE; header_theme_version=CLOSE; PVID=1; DedeUserID=21995788; DedeUserID__ckMd5=74533ce01e81c457; LIVE_BUVID=AUTO2417163479959397; fingerprint=d4fd28d46f0cdf7f6c4c4bd4cffc4232; browser_resolution=1494-765; buvid3=BCD23078-F90D-C047-149F-3130A5299F6B27399infoc; b_nut=1728617127; _uuid=C9D3E15B-812C-7E68-EAB3-824B5A97E53428151infoc; buvid_fp=717cf89234532f9f32c7a8860c589d96; rpdid=|(umRR)Yu)|k0J\\'u~JkY|Rl)m; enable_feed_channel=ENABLE; CURRENT_FNVAL=4048; SESSDATA=ae848d53%2C1762744517%2Cc9f15%2A52CjB4iZUl6gWWq_YetxVdmpa7IobyabdVgg6fuXoD4jxV7mxxlFTpkma4X8zpETZ40RISVkY4NVdVN2RzTWo4TEZTdWRfdnRuYXl4c205ZDFxMVp4NDFGWEVUUVB6TVBUcl9vSmREdEh4R3BKTWZiZHVNT1ZpdHdQcHBZX1lsZEF0aERuZk9UR0tnIIEC; bili_jct=7954f256b7"
        
        custom_cookie = cookie_json or ''
        headers = {
            'authority': 'mall.bilibili.com',
            'method': 'POST',
            'path': '/mall-magic-c/internet/c2c/v2/list',
            'scheme': 'https',
            'accept': 'application/json, text/plain, */*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'cookie': custom_cookie or default_cookie,
            'dnt': '1',
            'origin': 'https://mall.bilibili.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://mall.bilibili.com/neul-next/index.html?page=magic-market_index',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'sec-gpc': '1',
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'
        }

        if utils.task_status["status"] == "running":
            return jsonify({"error": "任务已在运行"}), 400
        init_db()
        thread = threading.Thread(target=run_spider, args=(headers, mode))
        thread.daemon = True
        thread.start()
        utils.update_task_status(status="running")
        return jsonify({"status": "任务已启动"})

    except Exception as e:
        return jsonify({"error": f"请求处理失败: {str(e)}"}), 400

@app.route('/api/stop', methods=['POST'])
def stop_task():
    """主动停止任务接口"""
    if utils.task_status["status"] != "running":
        return jsonify({"error": "任务未运行"}), 400

    share.stop_flag = True  # 直接修改 utils 模块中的 stop_flag
    return jsonify({"status": "停止信号已发送"})

@app.route('/api/status', methods=['GET'])
def get_status():
    """获取当前任务状态"""
    return jsonify({
        **utils.task_status,
        "current_time": time.strftime("%Y-%m-%d %H:%M:%S")
    })

@app.route('/api/reset', methods=['POST'])
def reset_next_id():
    """清空 last_next_id 表"""
    conn = db_pool.getconn()
    cursor = conn.cursor()
    try:
        cursor.execute("DELETE FROM last_next_id")
        conn.commit()
        return jsonify({"status": "last_next_id 已清空"})
    except Exception as e:
        conn.rollback()
        return jsonify({"error": f"清空失败: {str(e)}"}), 500
    finally:
        db_pool.putconn(conn)

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=5000)