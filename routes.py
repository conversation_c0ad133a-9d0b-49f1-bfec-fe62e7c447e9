# routes.py
from flask import Flask, request, jsonify
import threading
import time
import logging
from typing import Dict, Any, Tuple

import share
from spider import run_spider
from utils import get_task_status, update_task_status
from db import db_pool, init_db
from config import config

try:
    from monitor import performance_monitor
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False

app = Flask(__name__)
logger = logging.getLogger(__name__)

# 启动性能监控
if MONITORING_AVAILABLE:
    performance_monitor.start_monitoring()

def validate_request_data(data: Dict[str, Any]) -> Tuple[str, str]:
    """验证请求数据并返回cookie和mode"""
    cookie = data.get('cookie', '') if data else ''
    mode = data.get('mode', 'restart') if data else 'restart'

    # 验证mode参数
    if mode not in ['restart', 'continue']:
        raise ValueError(f"无效的模式参数: {mode}")

    return cookie, mode

def build_headers(custom_cookie: str) -> Dict[str, str]:
    """构建请求头"""
    headers = config.DEFAULT_HEADERS.copy()
    headers['cookie'] = custom_cookie or config.DEFAULT_COOKIE
    return headers

@app.route('/api/start', methods=['POST'])
def start_task():
    """启动爬虫任务"""
    try:
        # 验证请求数据
        request_data = request.get_json() if request.is_json else {}
        cookie, mode = validate_request_data(request_data)

        # 检查任务状态
        current_status = get_task_status()
        if current_status["status"] == "running":
            return jsonify({"error": "任务已在运行"}), 400

        # 构建请求头
        headers = build_headers(cookie)

        # 初始化数据库
        init_db()

        # 启动爬虫线程
        thread = threading.Thread(target=run_spider, args=(headers, mode))
        thread.daemon = True
        thread.start()

        # 更新任务状态
        update_task_status(status="running")

        logger.info(f"爬虫任务已启动，模式: {mode}")
        return jsonify({"status": "任务已启动", "mode": mode})

    except ValueError as ve:
        logger.error(f"参数验证失败: {ve}")
        return jsonify({"error": f"参数错误: {str(ve)}"}), 400
    except Exception as e:
        logger.error(f"启动任务失败: {e}", exc_info=True)
        return jsonify({"error": f"请求处理失败: {str(e)}"}), 500

@app.route('/api/stop', methods=['POST'])
def stop_task():
    """主动停止任务接口"""
    try:
        current_status = get_task_status()
        if current_status["status"] != "running":
            return jsonify({"error": "任务未运行"}), 400

        share.stop_flag = True
        logger.info("停止信号已发送")
        return jsonify({"status": "停止信号已发送"})

    except Exception as e:
        logger.error(f"停止任务失败: {e}", exc_info=True)
        return jsonify({"error": f"停止任务失败: {str(e)}"}), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """获取当前任务状态"""
    try:
        status = get_task_status()
        status["current_time"] = time.strftime("%Y-%m-%d %H:%M:%S")

        # 计算运行时间
        if status.get("start_time") and status["status"] == "running":
            status["running_duration"] = time.time() - status["start_time"]
        elif status.get("start_time") and status.get("end_time"):
            status["total_duration"] = status["end_time"] - status["start_time"]

        return jsonify(status)

    except Exception as e:
        logger.error(f"获取状态失败: {e}", exc_info=True)
        return jsonify({"error": f"获取状态失败: {str(e)}"}), 500

@app.route('/api/reset', methods=['POST'])
def reset_next_id():
    """清空 last_next_id 表"""
    conn = db_pool.getconn()
    cursor = conn.cursor()
    try:
        cursor.execute("DELETE FROM last_next_id")
        conn.commit()
        return jsonify({"status": "last_next_id 已清空"})
    except Exception as e:
        conn.rollback()
        return jsonify({"error": f"清空失败: {str(e)}"}), 500
    finally:
        db_pool.putconn(conn)

@app.route('/api/metrics', methods=['GET'])
def get_metrics():
    """获取性能指标"""
    try:
        if not MONITORING_AVAILABLE:
            return jsonify({"error": "性能监控不可用"}), 503

        current_metrics = performance_monitor.get_current_metrics()
        return jsonify(current_metrics)

    except Exception as e:
        logger.error(f"获取指标失败: {e}", exc_info=True)
        return jsonify({"error": f"获取指标失败: {str(e)}"}), 500

@app.route('/api/metrics/history', methods=['GET'])
def get_metrics_history():
    """获取指标历史"""
    try:
        if not MONITORING_AVAILABLE:
            return jsonify({"error": "性能监控不可用"}), 503

        limit = request.args.get('limit', 10, type=int)
        limit = min(max(limit, 1), 100)  # 限制在1-100之间

        history = performance_monitor.get_metrics_history(limit)
        return jsonify({"history": history, "count": len(history)})

    except Exception as e:
        logger.error(f"获取指标历史失败: {e}", exc_info=True)
        return jsonify({"error": f"获取指标历史失败: {str(e)}"}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        conn = db_pool.getconn()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        db_pool.putconn(conn)

        # 获取当前状态
        status = get_task_status()

        health_info = {
            "status": "healthy",
            "database": "connected",
            "task_status": status["status"],
            "monitoring": "enabled" if MONITORING_AVAILABLE else "disabled",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        return jsonify(health_info)

    except Exception as e:
        logger.error(f"健康检查失败: {e}", exc_info=True)
        return jsonify({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }), 500

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=5000)