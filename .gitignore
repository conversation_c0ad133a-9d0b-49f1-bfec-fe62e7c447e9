# Python 编译文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# 环境变量文件
.env
.env.local
.env.*.local

# 依赖文件
Pipfile.lock

# 日志和缓存
*.log
*.swp
*.swo
logs/

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 配置
.vscode/
.idea/
*.iml
*.sublime-project
*.sublime-workspace

# 测试和构建产物
coverage/
htmlcov/
.coverage
.coverage.*
.cache
.pytest_cache/
.tox/
dist/
build/
*.egg-info/
.eggs/

# Docker
.dockerignore

# 数据库文件
*.db
*.sqlite3

# 临时文件
*.tmp
*.temp
.tmp/